<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="ANALYZE_TEST_SOURCES" value="false" />
  </component>
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="PinXiXi_shop:war exploded" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f257360f-5dcd-4f0b-a7e7-f72bb9ab17c1" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
        <option value="Jsp File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;yycy134679&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/yycy134679/PinXiXi_shop.git&quot;,
    &quot;accountId&quot;: &quot;43888b1d-fddb-4044-9079-3fefd066a6b6&quot;
  }
}</component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="true" />
    <option name="FILTER_DEBUG" value="true" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2xH0Kzsx5OvKZJJ3JlLAbxUM6W8" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="sortByType" value="true" />
    <option name="sortKey" value="BY_TYPE" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.AddressServiceImplTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AddressServiceImplTest.saveOrUpdateAddress_shouldSaveNewAddress_whenAddressNotExists.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.CartServiceImplTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.DBUtilTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.DBUtilTest.testGetConnection_WhenCalledMultipleTimes_ShouldWork.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ProductDaoImplTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ProductServiceImplTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UserDaoImplTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UserDaoImplTest.testFindByUsername_existingUser.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UserServiceImplTest.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Tomcat 服务器.Tomcat 9.0.58.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/移动云盘同步盘/课程/Java Web/PinXiXi_shop/src/main/webapp/WEB-INF&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;File.Encoding&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\移动云盘同步盘\课程\Java Web\PinXiXi_shop\src\main\webapp\WEB-INF" />
      <recent name="D:\移动云盘同步盘\课程\Java Web\PinXiXi_shop" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\移动云盘同步盘\课程\Java Web\PinXiXi_shop\src\main\webapp" />
    </key>
  </component>
  <component name="RunManager" selected="Tomcat 服务器.Tomcat 9.0.58">
    <configuration name="AddressServiceImplTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="PinXiXi_shop" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yycy.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.yycy.service.impl" />
      <option name="MAIN_CLASS_NAME" value="com.yycy.service.impl.AddressServiceImplTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AddressServiceImplTest.saveOrUpdateAddress_shouldSaveNewAddress_whenAddressNotExists" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="PinXiXi_shop" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yycy.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.yycy.service.impl" />
      <option name="MAIN_CLASS_NAME" value="com.yycy.service.impl.AddressServiceImplTest" />
      <option name="METHOD_NAME" value="saveOrUpdateAddress_shouldSaveNewAddress_whenAddressNotExists" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CartServiceImplTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="PinXiXi_shop" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yycy.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.yycy.service.impl" />
      <option name="MAIN_CLASS_NAME" value="com.yycy.service.impl.CartServiceImplTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserDaoImplTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="PinXiXi_shop" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yycy.dao.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.yycy.dao.impl" />
      <option name="MAIN_CLASS_NAME" value="com.yycy.dao.impl.UserDaoImplTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceImplTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="PinXiXi_shop" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yycy.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.yycy.service.impl" />
      <option name="MAIN_CLASS_NAME" value="com.yycy.service.impl.UserServiceImplTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tomcat 9.0.58" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 9.0.58" ALTERNATIVE_JRE_ENABLED="true" ALTERNATIVE_JRE_PATH="17">
      <deployment>
        <artifact name="PinXiXi_shop:war exploded">
          <settings>
            <option name="CONTEXT_PATH" value="/" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="711cdd28-5798-4f31-9cf6-08c2d3a6b70e" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="AppServerDebuggerRunner">
        <option name="DEBUG_PORT" value="52347" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="AppServerDebuggerRunner">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="PinXiXi_shop:war exploded" />
        </option>
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.AddressServiceImplTest" />
      <item itemvalue="JUnit.AddressServiceImplTest.saveOrUpdateAddress_shouldSaveNewAddress_whenAddressNotExists" />
      <item itemvalue="JUnit.CartServiceImplTest" />
      <item itemvalue="JUnit.UserDaoImplTest" />
      <item itemvalue="JUnit.UserServiceImplTest" />
      <item itemvalue="Tomcat 服务器.Tomcat 9.0.58" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.AddressServiceImplTest" />
        <item itemvalue="JUnit.AddressServiceImplTest.saveOrUpdateAddress_shouldSaveNewAddress_whenAddressNotExists" />
        <item itemvalue="JUnit.UserServiceImplTest" />
        <item itemvalue="JUnit.CartServiceImplTest" />
        <item itemvalue="JUnit.UserDaoImplTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f257360f-5dcd-4f0b-a7e7-f72bb9ab17c1" name="更改" comment="" />
      <created>1747579632818</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747579632818</updated>
      <workItem from="1747579634282" duration="1433000" />
      <workItem from="1747640424712" duration="29000" />
      <workItem from="1747641979736" duration="1383000" />
      <workItem from="1747643385998" duration="2933000" />
      <workItem from="1747652880742" duration="94000" />
      <workItem from="1747653048895" duration="1128000" />
      <workItem from="1747656484547" duration="212000" />
      <workItem from="1747664896356" duration="506000" />
      <workItem from="1747665627852" duration="695000" />
      <workItem from="1747718921719" duration="91000" />
      <workItem from="1747719828071" duration="290000" />
      <workItem from="1747739501166" duration="1189000" />
      <workItem from="1747741097894" duration="650000" />
      <workItem from="1747742057813" duration="709000" />
      <workItem from="1747742809030" duration="7222000" />
      <workItem from="1747752746276" duration="274000" />
      <workItem from="1747753691152" duration="3952000" />
      <workItem from="1747758230731" duration="1650000" />
      <workItem from="1747760428910" duration="812000" />
      <workItem from="1747762023061" duration="197000" />
      <workItem from="1747826194897" duration="12776000" />
      <workItem from="1747899692489" duration="1137000" />
      <workItem from="1747922398766" duration="5498000" />
      <workItem from="1747986564446" duration="2202000" />
      <workItem from="1747989322261" duration="3756000" />
      <workItem from="1747996885779" duration="542000" />
      <workItem from="1747998005237" duration="2085000" />
      <workItem from="1748245034910" duration="11000" />
      <workItem from="1748269451679" duration="1166000" />
      <workItem from="1748321356685" duration="1547000" />
      <workItem from="1748625362566" duration="44000" />
      <workItem from="1749109231525" duration="128000" />
      <workItem from="1749109370068" duration="913000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>