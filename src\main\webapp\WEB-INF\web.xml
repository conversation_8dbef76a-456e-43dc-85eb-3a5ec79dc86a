<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>PinXiXi Shop</display-name>

    <!-- 1. 配置字符编码过滤器 -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>com.yycy.filter.CharacterEncodingFilter</filter-class>
        <!-- (可选) 如果过滤器需要初始化参数
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        -->
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 2. 配置欢迎文件列表 -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- 新增：错误页面配置 -->
    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/jsp/error.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/jsp/error.jsp</location>
    </error-page>
    <error-page>
        <exception-type>java.lang.Throwable</exception-type>
        <location>/WEB-INF/jsp/error.jsp</location>
    </error-page>
    <!-- 你也可以为更具体的异常类型配置错误页面，例如：
    <error-page>
        <exception-type>java.sql.SQLException</exception-type>
        <location>/WEB-INF/jsp/databaseError.jsp</location>
    </error-page>
    -->

    <!-- 3. 配置认证过滤器 -->
    <filter>
        <filter-name>AuthenticationFilter</filter-name>
        <filter-class>com.yycy.filter.AuthenticationFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>AuthenticationFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 4. 注册 HomeServlet -->
    <servlet>
        <servlet-name>HomeServlet</servlet-name>
        <servlet-class>com.yycy.controller.HomeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HomeServlet</servlet-name>
        <url-pattern>/home</url-pattern>
        <!-- <url-pattern>/index</url-pattern> 可选 -->
    </servlet-mapping>

    <!-- 5. 注册 RegisterServlet -->
    <!--
    <servlet>
        <servlet-name>RegisterServlet</servlet-name>
        <servlet-class>com.yycy.controller.RegisterServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RegisterServlet</servlet-name>
        <url-pattern>/register</url-pattern>
    </servlet-mapping>
    -->

    <!-- 后续会在这里添加 Servlet 和其他 Filter 的配置 -->

</web-app>