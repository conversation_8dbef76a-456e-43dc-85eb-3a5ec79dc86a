PinXiXi_shop/
├── src/
│ ├── main/
│ │ ├── java/
│ │ │ └── com.yycy
│ │ │ ├── controller/ // 控制器层 (Servlets): 处理 HTTP 请求，调用服务层，并转发到 JSP 视图
│ │ │ │ ├── HomeServlet.java // 处理首页展示、商品搜索等逻辑
│ │ │ │ ├── ProductServlet.java // 处理商品详情页展示逻辑
│ │ │ │ ├── CartServlet.java // 处理加入购物车、查看购物车、修改数量、删除商品等逻辑
│ │ │ │ ├── CheckoutServlet.java // 处理结算页面展示和模拟支付的逻辑
│ │ │ │ ├── LoginServlet.java // 处理用户登录请求
│ │ │ │ ├── RegisterServlet.java // 处理用户注册请求
│ │ │ │ ├── LogoutServlet.java // 处理用户注销请求
│ │ │ │ └── ProfileServlet.java // 处理个人中心的用户信息修改、密码更改、头像上传、地址管理等
│ │ │ │
│ │ │ ├── dao/ // 数据访问层 (DAO): 定义数据访问接口和其实现
│ │ │ │ ├── IUserDao.java // 用户数据访问操作接口
│ │ │ │ ├── IProductDao.java // 商品数据访问操作接口
│ │ │ │ ├── IAddressDao.java // 地址数据访问操作接口
│ │ │ │ │
│ │ │ │ └── impl/ // DAO 接口的具体实现类
│ │ │ │ ├── UserDaoImpl.java // IUserDao 接口的 JDBC 实现，负责用户表操作
│ │ │ │ ├── ProductDaoImpl.java // IProductDao 接口的 JDBC 实现，负责商品表操作
│ │ │ │ └── AddressDaoImpl.java // IAddressDao 接口的 JDBC 实现，负责地址表操作
│ │ │ │
│ │ │ ├── entity/ // 实体类 (POJOs): 与数据库表对应的 Java 对象
│ │ │ │ ├── User.java // 用户实体类，对应用户表，password 字段直接存储明文密码，不包含 salt 或哈希相关字段
│ │ │ │ ├── Product.java // 商品实体类，对应商品表，包含 salesVolume (销量) 字段，该字段值主要由数据库预设，应用层面不进行更新
│ │ │ │ ├── Cart.java // 购物车对象，包含多个购物车项，购物车数据存储于 Session，非持久化
│ │ │ │ ├── CartItem.java // 购物车项对象，表示购物车中的一件商品及其数量
│ │ │ │ └── Address.java // 地址实体类，对应地址表
│ │ │ │
│ │ │ ├── service/ // 服务层: 封装业务逻辑，定义服务接口和其实现
│ │ │ │ ├── IUserService.java // 用户相关业务逻辑服务接口
│ │ │ │ ├── IProductService.java // 商品相关业务逻辑服务接口
│ │ │ │ ├── ICartService.java // 购物车相关业务逻辑服务接口（实现为操作 Session 中的 Cart 对象，无 CartDao）
│ │ │ │ ├── IAddressService.java // 地址相关业务逻辑服务接口
│ │ │ │ │
│ │ │ │ └── impl/ // 服务接口的具体实现类
│ │ │ │ ├── UserServiceImpl.java // IUserService 接口的实现，处理用户登录、注册等业务
│ │ │ │ ├── ProductServiceImpl.java// IProductService 接口的实现，处理商品查询等业务
│ │ │ │ ├── CartServiceImpl.java // ICartService 接口的实现，处理购物车操作业务
│ │ │ │ └── AddressServiceImpl.java// IAddressService 接口的实现，处理地址管理业务
│ │ │ │
│ │ │ ├── filter/ // 过滤器 (Servlet Filters)
│ │ │ │ └── AuthenticationFilter.java // 认证过滤器，例如检查用户是否登录
│ │ │ │
│ │ │ └── util/ // 工具类
│ │ │ └── DBUtil.java // 数据库连接和关闭工具类
│ │ │
│ │ ├── webapp/ // Web 应用的根目录，包含 JSP、静态资源等
│ │ │ ├── WEB-INF/
│ │ │ │ ├── web.xml // Web 应用部署描述文件，配置 Servlet、Filter 等
│ │ │ │ ├── lib/ // 存放第三方库，如 JSTL、MySQL 驱动等
│ │ │ │ └── jsp/ // 存放 JSP 视图文件
│ │ │ │ ├── home.jsp // 网站首页视图
│ │ │ │ ├── productDetail.jsp // 商品详情页视图
│ │ │ │ ├── cart.jsp // 购物车页面视图
│ │ │ │ ├── checkout.jsp // 结算(模拟支付)页面视图
│ │ │ │ ├── paymentSuccess.jsp // 支付成功提示页面视图
│ │ │ │ ├── login.jsp // 用户登录页面视图
│ │ │ │ ├── register.jsp // 用户注册页面视图
│ │ │ │ ├── profile.jsp // 个人中心页面视图
│ │ │ │ ├── error.jsp // 通用错误提示页面视图
│ │ │ │ └── productNotFound.jsp // 商品未找到提示页面视图
│ │ │ ├── images/ // 存放图片资源
│ │ │ │ ├── logo.png // 网站 Logo 图片
│ │ │ │ ├── banner/ // 首页轮播图图片存放目录
│ │ │ │ ├── products/ // 商品图片存放目录
│ │ │ │ └── tou_xiang/ // 用户头像图片存放目录
│ │ │ └── index.jsp // 应用的默认入口文件，通常重定向到首页 Servlet
│
├── database/
│ └── PinXiXi_shop.sql // 数据库表结构定义和初始数据插入的 SQL 脚本
│
├── README.md // 项目说明文件，介绍项目、如何运行等
└── pom.xml // Maven 构建配置文件
