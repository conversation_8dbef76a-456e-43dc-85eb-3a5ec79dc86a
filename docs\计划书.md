# 实现计划 (IMP) - PinXiXi 电商平台

**项目代号:** PinXiXi_Shop
**版本:** 1.0
**制定日期:** 2025-05-18

**任务状态说明:**
*   `[]` 未开始
*   `[-]` 进行中
*   `[✅]` 已完成
*   👤 需要人工主导完成或重点关注的任务

## 0. 项目前期准备与环境配置

*   `[✅]` 确认开发环境 (JDK 8+ (推荐 LTS 版本如 JDK 11 或 17), Maven 3.6+, IDE如IntelliJ IDEA Ultimate或Eclipse for Java EE) 已正确安装和配置。 👤
*   `[✅]` 确认 MySQL 8 服务器已安装并正在运行，数据库客户端 (如 Navicat, DBeaver, MySQL Workbench) 可用。 👤
*   `[✅]` **初始化项目数据库:** 👤
    *   使用数据库客户端连接到 MySQL 服务器。
    *   创建名为 `pinxixi_shop` 的数据库 schema (字符集推荐 `utf8mb4`, 排序规则 `utf8mb4_unicode_ci`)。
    *   执行项目提供的 `PinXiXi_shop.sql` 脚本，完成表结构创建和初始数据导入。
    *   验证数据是否成功导入。
*   `[✅]` **创建 Maven Web 项目骨架:** 👤
    *   使用 IDE (如 IntelliJ IDEA) 创建一个新的 Maven Web Application 项目 (选择合适的 Java EE 版本，如 Java EE 8)。
    *   配置 `pom.xml` 文件，添加必要的依赖：
        *   `[✅]` `javax.servlet-api` (Servlet API, scope: provided)
        *   `[✅]` `javax.servlet.jsp-api` (JSP API, scope: provided)
        *   `[✅]` `javax.servlet:jstl` (JSTL)
        *   `[✅]` `mysql:mysql-connector-java` (MySQL JDBC驱动, 版本与MySQL服务器兼容)
        *   `[✅]` `commons-fileupload:commons-fileupload` (用于文件上传)
        *   `[✅]` `commons-io:commons-io` (可选, commons-fileupload 可能依赖)
        *   `[✅]` (可选, 推荐) Logging framework: `org.slf4j:slf4j-api` + `ch.qos.logback:logback-classic` (或 Log4j2)。
    *   确保 Maven 依赖正确下载。
*   `[✅]` **配置项目部署:** 👤
    *   在 IDE 中配置 Tomcat 服务器 (版本 9.x 或与 Servlet API 兼容的版本)。
    *   将 Maven 项目构建的 WAR 包部署到 Tomcat。
    *   启动 Tomcat 服务器并确保可以访问项目默认入口 (例如 `http://localhost:8080/PinXiXi_shop/`)。
*   `[✅]` **建立版本控制:** 👤
    *   在项目根目录初始化 Git 仓库 (`git init`)。
    *   创建 `.gitignore` 文件，忽略 IDE 特定文件、编译输出 (`target/`) 等。
    *   进行首次提交 (`git add .`, `git commit -m "Initial project setup"`).

## 1. 基础架构与核心组件

#### 1.1 项目包结构创建

*   `[✅]` 根据 `项目结构.md` 在 `src/main/java` 下创建基础包名 `com.yycy`。
*   `[✅]` 在 `com.yycy` 下创建子包: `controller`, `dao`, `dao.impl`, `entity`, `service`, `service.impl`, `filter`, `util`。
*   `[✅]` 在 `src/main/webapp` 下创建目录结构: `WEB-INF`, `WEB-INF/lib` (如果手动管理JSTL等jar包), `WEB-INF/jsp`, `images`, `images/banner`, `images/products`, `images/tou_xiang`。
*   `[✅]` 将预置的图片资源（logo.png, banner图片, 商品图片, 示例头像）拷贝到对应的 `images` 子目录下。 👤

#### 1.2 实体类 (Entity / POJO) 定义

*   `[✅]` **User.java (`com.yycy.entity`):**
    *   定义 `id (int)`, `username (String)`, `password (String)`, `nickname (String)`, `email (String)`, `phone (String)`, `gender (String)`, `avatar_path (String)`, `created_at (Timestamp)`, `updated_at (Timestamp)` 字段。
    *   提供无参构造函数、全参构造函数 (或按需)、所有字段的 getter/setter 方法, `toString()` (可选, 便于调试)。
*   `[✅]` **Product.java (`com.yycy.entity`):**
    *   定义 `id (int)`, `name (String)`, `description (String)`, `price (BigDecimal)`, `image_url (String)`, `sales_volume (int)`, `created_at (Timestamp)`, `updated_at (Timestamp)` 字段。
    *   提供构造函数、getter/setter 方法, `toString()`。
*   `[✅]` **Address.java (`com.yycy.entity`):**
    *   定义 `id (int)`, `user_id (int)`, `province (String)`, `city (String)`, `district (String)`, `detail_address (String)`, `created_at (Timestamp)`, `updated_at (Timestamp)` 字段。
    *   提供构造函数、getter/setter 方法, `toString()`。
*   `[✅]` **CartItem.java (`com.yycy.entity` - 非持久化):**
    *   定义 `product (Product)` 和 `quantity (int)` 字段。
    *   提供构造函数、getter/setter 方法。
    *   添加计算小计金额的方法 `public BigDecimal getSubtotal() { return product.getPrice().multiply(new BigDecimal(quantity)); }`。
*   `[✅]` **Cart.java (`com.yycy.entity` - 非持久化, 存储于 Session):**
    *   定义 `private Map<Integer, CartItem> items = new LinkedHashMap<>();` (使用 `LinkedHashMap` 保持插入顺序，符合RFC购物车商品排序要求)。
    *   提供方法:
        *   `[✅]` `addItem(Product product, int quantity)`: 添加或更新商品项。
        *   `[✅]` `removeItem(int productId)`: 删除商品项。
        *   `[✅]` `updateItemQuantity(int productId, int quantity)`: 更新商品数量。
        *   `[✅]` `clearCart()`: 清空购物车。
        *   `[✅]` `getTotalPrice()`: 计算购物车总金额。
        *   `[✅]` `getItems()`: 获取所有购物车项。
        *   `[✅]` `getTotalItemsCount()`: 获取购物车中商品的总件数 (用于导航栏角标)。

#### 1.3 数据库工具类 (DBUtil.java - `com.yycy.util`)

*   `[✅]` 定义数据库连接参数为静态常量 (URL, USER, PASSWORD)。 **[技术考量]** 生产环境应使用更安全的配置方式。
*   `[✅]` 实现静态方法 `getConnection()`:
    *   加载 MySQL JDBC 驱动 (`Class.forName("com.mysql.cj.jdbc.Driver");`)。
    *   使用 `DriverManager.getConnection()` 获取连接。
    *   处理 `SQLException` 和 `ClassNotFoundException`。
*   `[✅]` 实现静态方法 `close(Connection conn, Statement stmt, ResultSet rs)`:
    *   分别检查 `rs`, `stmt`, `conn` 是否为 null，然后关闭。
    *   捕获并处理关闭时可能发生的 `SQLException`。
*   `[✅]` 实现静态方法 `close(Connection conn, Statement stmt)` (重载)。

#### 1.4 数据访问层 (DAO) 接口定义 (`com.yycy.dao`)

*   `[✅]` **IUserDao.java:**
    *   `User findByUsername(String username) throws SQLException;`
    *   `User findById(int id) throws SQLException;`
    *   `void save(User user) throws SQLException;` (用于注册)
    *   `void update(User user) throws SQLException;` (用于个人信息、密码、头像更新)
*   `[✅]` **IProductDao.java:**
    *   `List<Product> findAll() throws SQLException;`
    *   `Product findById(int id) throws SQLException;`
    *   `List<Product> findByNameContaining(String nameKeyword) throws SQLException;`
*   `[✅]` **IAddressDao.java:**
    *   `Address findByUserId(int userId) throws SQLException;`
    *   `void save(Address address) throws SQLException;`
    *   `void update(Address address) throws SQLException;`
    *   (RFC未明确删除，但单一地址管理下，更新即覆盖，或可添加 `deleteByUserId` 若有此需求)

#### 1.5 服务层 (Service) 接口定义 (`com.yycy.service`)

*   `[✅]` **IUserService.java:**
    *   `User login(String username, String password);`
    *   `boolean register(User user);` (参数可细化为 `String username, String password`)
    *   `User getUserById(int id);`
    *   `boolean updateUserProfile(User user);`
    *   `boolean changePassword(int userId, String oldPassword, String newPassword);`
    *   `boolean updateUserAvatar(int userId, String avatarPath);`
*   `[✅]` **IProductService.java:**
    *   `List<Product> getAllProducts();`
    *   `Product getProductById(int id);`
    *   `List<Product> searchProductsByName(String nameKeyword);`
*   `[✅]` **ICartService.java (操作 Session 中的 Cart 对象):**
    *   `Cart getCart(HttpSession session);`
    *   `void addProductToCart(HttpSession session, int productId, int quantity);`
    *   `void removeProductFromCart(HttpSession session, int productId);`
    *   `void updateProductQuantityInCart(HttpSession session, int productId, int newQuantity);`
    *   `void clearCart(HttpSession session);`
*   `[✅]` **IAddressService.java:**
    *   `Address getAddressByUserId(int userId);`
    *   `boolean saveOrUpdateAddress(Address address);`

#### 1.6 DAO 和 Service 的实现类骨架 (`com.yycy.dao.impl`, `com.yycy.service.impl`)

*   `[✅]` 创建 `UserDaoImpl.java`, `ProductDaoImpl.java`, `AddressDaoImpl.java` 并实现对应接口。方法体暂时留空或 `throw new UnsupportedOperationException();`。
*   `[✅]` 创建 `UserServiceImpl.java`, `ProductServiceImpl.java`, `CartServiceImpl.java`, `AddressServiceImpl.java` 并实现对应接口。
    *   在实现类中声明对应的 DAO 接口作为成员变量。
    *   提供构造函数或 setter 方法注入 DAO 依赖 (对于期末项目，简单 `new DaoImpl()` 也可接受，但不利于测试和扩展)。

#### 1.7 基础 JSP 页面与公共片段 (`src/main/webapp`)

*   `[✅]` **web.xml 配置 (初步):**
    *   配置欢迎文件列表: `<welcome-file-list><welcome-file>index.jsp</welcome-file></welcome-file-list>`。
    *   定义字符编码过滤器 `CharacterEncodingFilter`:
        *   `[✅]` 创建 `com.yycy.filter.CharacterEncodingFilter.java` 实现 `javax.servlet.Filter`。
        *   `[✅]` 在 `doFilter` 中设置 `request.setCharacterEncoding("UTF-8");` 和 `response.setContentType("text/html;charset=UTF-8");` (或 `response.setCharacterEncoding("UTF-8");`)。
        *   `[✅]` 在 `web.xml` 中注册此 Filter 并映射到 `/*`，确保它在其他业务 Filter 之前。
*   `[✅]` **index.jsp:** 内容为 `<% response.sendRedirect(request.getContextPath() + "/home"); %>` 或使用 JSTL `<c:redirect url="/home"/>`。
*   `[✅]` **WEB-INF/jsp/error.jsp:** 通用错误页面，可显示 `requestScope.errorMessage`。每个JSP页面将内联其完整的HTML结构，包括`<head>` (含Bootstrap CDN链接) 和 `<body>` (含导航栏和页脚逻辑)。
*   `[✅]` **WEB-INF/jsp/productNotFound.jsp:** 商品未找到提示页面。每个JSP页面将内联其完整的HTML结构。

#### 1.8 认证过滤器 (AuthenticationFilter.java - `com.yycy.filter`)

*   `[✅]` 创建 `AuthenticationFilter.java` 实现 `javax.servlet.Filter`。
*   `[✅]` 在 `init` 方法中初始化受保护路径列表和公共路径列表。
    *   `private Set<String> protectedPaths = new HashSet<>(Arrays.asList("/profile", "/cart", "/checkout", "/submitOrder", "/addToCart", "/updateCartItem", "/removeCartItem", "/saveAddress", "/changePassword", "/uploadAvatar"));` (根据实际Servlet路径调整, 特别是处理动作的路径)
    *   `private Set<String> publicPaths = new HashSet<>(Arrays.asList("/login", "/register", "/logout", "/home", "/productDetail", "/index.jsp"));`
*   `[✅]` 在 `doFilter` 方法中:
    *   获取 `HttpServletRequest req`, `HttpServletResponse res`, `HttpSession session`。
    *   获取请求的 `String servletPath = req.getServletPath();`。
    *   检查静态资源 (如 `/css/`, `/js/`, `/images/`)，如果是则 `chain.doFilter`。 (注意：如果项目中没有全局 `/css/` 和 `/js/` 目录，此检查可能需要调整或移除)
    *   `User loggedInUser = (User) session.getAttribute("loggedInUser");`
    *   如果 `servletPath` 在 `protectedPaths` 中且 `loggedInUser == null`:
        *   `session.setAttribute("redirectUrlAfterLogin", req.getRequestURI() + (req.getQueryString() != null ? "?" + req.getQueryString() : ""));` (可选：保存原始请求以便登录后跳转回去)
        *   `res.sendRedirect(req.getContextPath() + "/login");`
        *   `return;`
    *   否则, `chain.doFilter(request, response);`
*   `[✅]` 在 `web.xml` 中注册 `AuthenticationFilter` 并配置其 `url-pattern` 为 `/*`。确保它在 `CharacterEncodingFilter` 之后。

---

## 2. 首页模块 (Homepage Module)

#### 2.1 DAO 层实现 (ProductDaoImpl.java)

*   `[✅]` **实现 `findAll()` 方法:**
    *   使用 `DBUtil` 获取连接和关闭资源。
    *   SQL: `SELECT * FROM products ORDER BY id ASC` (或 RFC 默认顺序)。
    *   遍历 `ResultSet`, 创建 `Product` 对象并添加到 `List`。
    *   编写单元测试 (需要数据库连接或 Mock)。
*   `[✅]` **实现 `findByNameContaining(String nameKeyword)` 方法:**
    *   SQL: `SELECT * FROM products WHERE name LIKE ? ORDER BY id ASC`。
    *   使用 `PreparedStatement`，设置参数 `"%"+nameKeyword+"%"`。
    *   映射结果。
    *   编写单元测试。
*   `[✅]` **实现 `findById(int id)` 方法:** (商品详情模块会用到，此处一并实现)
    *   SQL: `SELECT * FROM products WHERE id = ?`。
    *   映射结果。
    *   编写单元测试。

#### 2.2 Service 层实现 (ProductServiceImpl.java)

*   `[✅]` 注入 `IProductDao productDao = new ProductDaoImpl();` (或通过构造函数注入)。
*   `[✅]` **实现 `getAllProducts()` 方法:** 调用 `productDao.findAll()`，处理 `SQLException` (例如包装成 RuntimeException 或返回空列表)。
*   `[✅]` **实现 `searchProductsByName(String nameKeyword)` 方法:** 调用 `productDao.findByNameContaining(nameKeyword)`。
*   `[✅]` **实现 `getProductById(int id)` 方法:** 调用 `productDao.findById(id)`。

#### 2.3 控制器层 (HomeServlet.java - `com.yycy.controller`)

*   `[✅]` 创建 `HomeServlet.java` 继承 `HttpServlet`。
*   `[✅]` 注入 `IProductService productService = new ProductServiceImpl();`。
*   `[✅]` **重写 `doGet(HttpServletRequest request, HttpServletResponse response)` 方法:**
    *   获取 `searchKeyword` 参数。
    *   调用 `productService` 获取商品列表。
    *   将商品列表存入 `request.setAttribute("products", productsList);`。
    *   若搜索无结果，设置 `request.setAttribute("noResultsMessage", "...");`。
    *   转发到 `/WEB-INF/jsp/home.jsp`。
*   `[✅]` (可选) `doPost` 调用 `doGet`。
*   `[✅]` 在 `web.xml` 中注册 `HomeServlet`，映射到 `/home` 和 `/index` (或只映射 `/home`，`index.jsp` 重定向到 `/home`)。

#### 2.4 视图层 (WEB-INF/jsp/home.jsp)

*   `[✅]` `home.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
    *   **Head:** HTML5 文档类型, `<meta charset="UTF-8">`, `<title>`, Bootstrap 5 CSS (CDN)。
    *   **Navigation:** Bootstrap Navbar, Logo, 搜索框, 动态链接 (登录/注册/个人中心/购物车/退出)。
*   `[✅]` **轮播图:** 使用 Bootstrap Carousel，嵌入 3 张固定的 `images/banner/` 图片。
*   `[✅]` **商品列表:**
    *   使用 Bootstrap Grid (`<div class="row">`) 和 Cards (`<div class="col-md-3 mb-4"> <div class="card"> ... </div> </div>`)。
    *   JSTL `c:forEach var="product" items="${products}"`:
        *   Card Image: `<img src="${pageContext.request.contextPath}/${product.imageUrl}" class="card-img-top">`
        *   Card Body:
            *   Name: `<h5 class="card-title"><a href="${pageContext.request.contextPath}/productDetail?id=${product.id}">${product.name}</a></h5>`
            *   Price: `<p class="card-text">¥${product.price}</p>`
            *   "加入购物车"按钮: `<a href="${pageContext.request.contextPath}/cart?action=add&productId=${product.id}&quantity=1" class="btn btn-primary add-to-cart-btn">加入购物车</a>` (后续JS会增强此按钮，判断登录状态)。
    *   JSTL `c:if test="${not empty noResultsMessage}"`: 显示无结果提示。
*   `[✅]` **JavaScript for "加入购物车" (在 `home.jsp` 内):**
    *   为 `add-to-cart-btn` 添加点击事件监听。
    *   检查 `sessionScope.loggedInUser` (可以通过后端JSP输出一个JS变量判断，或者按钮的 `href` 根据登录状态动态生成)。
    *   如果未登录，`alert("请先登录！"); window.location.href = "${pageContext.request.contextPath}/login"; return false;`。
    *   (可选 AJAX 加入购物车以获得更好体验，但对于期末项目，直接跳转也可)。
*   `[✅]` **Footer:** `<footer>` 标签包含版权信息, Bootstrap 5 JS Bundle (CDN)。
*   `[✅]` **测试:** 首页展示、搜索、轮播、商品链接、未登录时加入购物车跳转。

---

## 3. 用户认证模块 (Registration, Login, Logout)

#### 3.1 DAO 层实现 (UserDaoImpl.java)

*   `[✅]` **实现 `findByUsername(String username)` 方法:** SQL `SELECT * FROM users WHERE username = ?`。
*   `[✅]` **实现 `save(User user)` 方法:** SQL `INSERT INTO users (username, password, created_at, updated_at) VALUES (?, ?, NOW(), NOW())` (注册时仅存用户名和密码，其他信息后续补充，`NOW()`用于时间戳)。
*   `[✅]` **实现 `findById(int id)` 方法:** SQL `SELECT * FROM users WHERE id = ?`。
*   `[✅]` **实现 `update(User user)` 方法:** (个人中心用，此处先实现骨架) SQL `UPDATE users SET nickname=?, email=?, phone=?, gender=?, avatar_path=?, updated_at=NOW() WHERE id=?` (根据实际更新字段调整)。修改密码是独立逻辑。

#### 3.2 Service 层实现 (UserServiceImpl.java)

*   `[✅]` 注入 `IUserDao userDao = new UserDaoImpl();`。
*   `[✅]` **实现 `register(User user)` (或 `register(String username, String password)`) 方法:**
    *   检查 `userDao.findByUsername(username)` 是否已存在。
    *   校验密码长度 (>=6)。
    *   创建 `User` 对象，设置用户名和密码。
    *   调用 `userDao.save(newUser)`。
    *   **[安全提示]** RFC要求明文密码。实际项目中应哈希密码。
*   `[✅]` **实现 `login(String username, String password)` 方法:**
    *   `User user = userDao.findByUsername(username);`
    *   如果 `user != null` 且 `user.getPassword().equals(password)` (明文比较)，返回 `user`。否则返回 `null`。
*   `[✅]` **实现 `getUserById(int id)` 方法:** 调用 `userDao.findById(id)`。

#### 3.3 注册模块 (RegisterServlet.java & WEB-INF/jsp/register.jsp)

*   `[✅]` **WEB-INF/jsp/register.jsp:**
    *   `register.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
    *   表单 (`method="post" action="${pageContext.request.contextPath}/register"`): 用户名, 密码, 确认密码输入框。
    *   显示错误消息 (`${requestScope.errorMessage}` 或 `${requestScope.errors.fieldName}`), 成功消息。
    *   "已有账号？去登录"链接。
*   `[✅]` **RegisterServlet.java:**
    *   `doGet`: 转发到 `register.jsp`。
    *   `doPost`:
        *   获取参数, 后端校验 (非空, 密码长度, 两次密码一致)。
        *   校验失败则设置错误信息并转发回 `register.jsp`。
        *   调用 `userService.register(username, password)`。
        *   成功: 调用 `userService.login(username, password)` 获取完整User对象, 存入 Session (`session.setAttribute("loggedInUser", registeredUser)`), 重定向到 `/profile`。
        *   失败 (用户名已存在): 设置错误信息，转发回 `register.jsp`。
*   `[✅]` `web.xml`: 注册 `RegisterServlet` 到 `/register`。
*   `[✅]` **测试:** 各种校验，重复注册，成功注册并自动登录跳转。

#### 3.4 登录模块 (LoginServlet.java & WEB-INF/jsp/login.jsp)

*   `[✅]` **WEB-INF/jsp/login.jsp:**
    *   `login.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
    *   表单 (`method="post" action="${pageContext.request.contextPath}/login"`): 用户名, 密码输入框。
    *   显示错误消息 (`${requestScope.errorMessage}`), 成功消息。
    *   "没有账号？去注册"链接。
*   `[✅]` **LoginServlet.java:**
    *   `doGet`: 转发到 `login.jsp`。
    *   `doPost`:
        *   获取参数。
        *   调用 `userService.login(username, password)`。
        *   成功: `User user` 对象存入 Session, 重定向到 `/home` (或之前保存的 `redirectUrlAfterLogin`)。
        *   失败: 设置错误信息，转发回 `login.jsp`。
*   `[✅]` `web.xml`: 注册 `LoginServlet` 到 `/login`。
*   `[✅]` **测试:** 错误凭证，成功登录，导航栏更新，访问受保护页面。

#### 3.5 登出模块 (LogoutServlet.java)

*   `[✅]` **LogoutServlet.java:**
    *   `doGet` (或 `doPost`): `session.invalidate()`, 重定向到 `/home`。
*   `[✅]` `web.xml`: 注册 `LogoutServlet` 到 `/logout`。
*   `[✅]` **测试:** 登出后 Session 失效，导航栏更新。

#### 3.6 更新 AuthenticationFilter (已在1.8中初步完成)

*   `[✅]` 仔细检查 `protectedPaths` 和 `publicPaths` (或 `excludedPaths`) 是否覆盖所有情况，特别是对 `action` 参数的 Servlet。
*   `[✅]` **测试过滤器:** 彻底测试所有路径的访问控制。

---

## 4. 商品详情模块 (Product Detail Module)

#### 4.1 控制器层 (ProductServlet.java - `com.yycy.controller`)

*   `[✅]` 创建 `ProductServlet.java` (或 `ProductDetailServlet.java`)。
*   `[✅]` 注入 `IProductService productService = new ProductServiceImpl();`。
*   `[✅]` **`doGet` 方法:**
    *   获取 `productId` 参数。校验是否为有效数字。
    *   调用 `productService.getProductById(id)`。
    *   如果商品不存在，转发到 `/WEB-INF/jsp/productNotFound.jsp`。
    *   如果商品存在，将 `product` 对象存入 `request`。
    *   **迷你商品导航列表:** 获取所有商品 `List<Product> allProducts = productService.getAllProducts();` 并存入 `request.setAttribute("allProductsForNav", allProducts);`。
    *   转发到 `/WEB-INF/jsp/productDetail.jsp`。
*   `[✅]` `web.xml`: 注册 `ProductServlet` 到 `/productDetail`。

#### 4.2 视图层 (WEB-INF/jsp/productDetail.jsp)

*   `[✅]` `productDetail.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
*   `[✅]` **页面布局 (例如 Bootstrap Grid: 左侧图片，右侧信息):**
    *   **左侧图片区域:**
        *   主图片: `<img id="mainProductImage" src="${pageContext.request.contextPath}/${product.imageUrl}" class="img-fluid">`。
        *   缩略图列表 (假设商品图片URL格式为 `base_url.jpg`, `base_url_thumb1.jpg`, `base_url_thumb2.jpg` 或从数据库获取多图信息。RFC未明确多图来源，简化处理：可展示同一主图的多个副本或预设几张不同图片路径):
            *   `<img src="..." class="thumbnail-img" data-large-src="...">` (点击缩略图用JS更新 `mainProductImage` 的 `src`)。
            *   RFC 提及 3-4 张缩略图，若无额外图片字段，可重复主图或预留静态图片。
    *   **右侧信息区域:**
        *   商品名称: `<h1>${product.name}</h1>`。
        *   商品价格: `<p class="h3">¥${product.price}</p>`。
        *   商品详细描述: `<p>${product.description}</p>`。
        *   **数量选择器:**
            *   `<button id="decreaseQty">-</button> <input type="number" id="quantity" value="1" min="1"> <button id="increaseQty">+</button>`。
            *   JS 控制按钮行为和输入框值。
        *   **"加入购物车"按钮:** `<a href="#" id="addToCartDetailBtn" class="btn btn-primary">加入购物车</a>`。
            *   JS: 点击时获取选定数量，构造 `href` 为 `/cart?action=add&productId=${product.id}&quantity=[selected_qty]`，然后判断登录并跳转/执行。
        *   **"立即购买"按钮:** `<a href="#" id="buyNowBtn" class="btn btn-success">立即购买</a>`。
            *   JS: 点击时获取选定数量，构造 `href` 为 `/checkout?productId=${product.id}&quantity=[selected_qty]` (直接到结算页)，然后判断登录并跳转/执行。
*   `[✅]` **迷你商品导航列表 (页面下方或侧边):**
    *   `<h3>其他商品</h3>`
    *   使用 JSTL `c:forEach var="navProd" items="${allProductsForNav}"`:
        *   显示小型商品卡片 (图片、名称)，链接到各自的 `/productDetail?id=${navProd.id}`。
*   `[✅]` **JavaScript (页面内):**
    *   缩略图点击切换主图。
    *   数量选择器逻辑。
    *   "加入购物车"和"立即购买"按钮的动态链接生成和登录检查。
*   `[✅]` **测试:** 访问有效/无效商品ID，图片展示，信息展示，数量调整，按钮功能 (未登录/已登录)。

---

## 5. 购物车模块 (Shopping Cart Module)

#### 5.1 Service 层实现 (CartServiceImpl.java)

*   `[✅]` 注入 `IProductService productService = new ProductServiceImpl();` (用于通过 productId 获取 Product 对象)。
*   `[✅]` **实现 `getCart(HttpSession session)`:**
    *   从 session 获取 `Cart` 对象。如果不存在，则 `new Cart()` 并存入 session。
*   `[✅]` **实现 `addProductToCart(HttpSession session, int productId, int quantity)`:**
    *   获取 `Cart`。
    *   `Product product = productService.getProductById(productId);`
    *   如果 `product != null`，调用 `cart.addItem(product, quantity)`。
*   `[✅]` **实现 `removeProductFromCart(HttpSession session, int productId)`:** 获取 `Cart`，调用 `cart.removeItem(productId)`。
*   `[✅]` **实现 `updateProductQuantityInCart(HttpSession session, int productId, int newQuantity)`:** 获取 `Cart`，调用 `cart.updateItemQuantity(productId, newQuantity)`。
*   `[✅]` **实现 `clearCart(HttpSession session)`:** 获取 `Cart`，调用 `cart.clearCart()`。

#### 5.2 控制器层 (CartServlet.java - `com.yycy.controller`)

*   `[✅]` 创建 `CartServlet.java`。
*   `[✅]` 注入 `ICartService cartService = new CartServiceImpl();`。
*   `[✅]` **`doGet` 和 `doPost` 方法 (或统一由一个方法处理 `action` 参数):**
    *   获取 `action` 参数 (`add`, `remove`, `update`, `clear`, `view` - `view` 为默认或无action时)。
    *   获取 `HttpSession session = request.getSession();`。
    *   **`action="add"`:**
        *   获取 `productId`, `quantity`。
        *   调用 `cartService.addProductToCart(session, productId, quantity)`。
        *   重定向回来源页 (如 `request.getHeader("Referer")`) 或商品列表页，并附带成功提示 (flash message)。
    *   **`action="remove"`:**
        *   获取 `productId`。
        *   调用 `cartService.removeProductFromCart(session, productId)`。
        *   重定向到 `/cart?action=view` (或 `/cart`)。
    *   **`action="update"`:**
        *   获取 `productId`, `newQuantity`。
        *   调用 `cartService.updateProductQuantityInCart(session, productId, newQuantity)`。
        *   重定向到 `/cart?action=view`。
    *   **`action="clear"`:**
        *   调用 `cartService.clearCart(session)`。
        *   重定向到 `/cart?action=view`。
    *   **`action="view"` (或默认):**
        *   `Cart cart = cartService.getCart(session);`
        *   `request.setAttribute("cart", cart);`
        *   转发到 `/WEB-INF/jsp/cart.jsp`。
*   `[✅]` `web.xml`: 注册 `CartServlet` 到 `/cart`。 (注意：AuthenticationFilter 会保护此路径)

#### 5.3 视图层 (WEB-INF/jsp/cart.jsp)

*   `[✅]` `cart.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
*   `[✅]` **检查购物车是否为空 (JSTL `c:choose`):**
    *   **`c:when test="${empty cart || empty cart.items}"`:** 显示"购物车为空"提示和"去逛逛"链接。
    *   **`c:otherwise`:** 显示购物车内容。
        *   **商品列表 (table 或 Bootstrap list group):**
            *   表头: 图片, 名称, 单价, 数量, 小计, 操作。
            *   JSTL `c:forEach var="entry" items="${cart.items}"` (Map.Entry) or `var="cartItem" items="${cart.items.values()}"` (CartItem)。
                *   `Product product = entry.value.product;` `int quantity = entry.value.quantity;`
                *   图片, 名称, 单价 (`${product.price}`).
                *   数量: `<input type="number" value="${quantity}" min="1" class="cart-item-qty" data-product-id="${product.id}">` (JS处理更新)。 或 +/- 按钮。
                *   小计: `<span class="cart-item-subtotal">${entry.value.subtotal}</span>` (JS实时更新)。
                *   操作: `<a href="${pageContext.request.contextPath}/cart?action=remove&productId=${product.id}" class="btn btn-danger btn-sm">删除</a>`。
        *   **购物车底部:**
            *   "清空购物车"按钮: `<a href="${pageContext.request.contextPath}/cart?action=clear" class="btn btn-warning">清空购物车</a>`。
            *   合计金额: `<h3>总计: <span id="cartTotal">${cart.totalPrice}</span></h3>` (JS实时更新)。
            *   "去结算"按钮: `<a href="${pageContext.request.contextPath}/checkout" class="btn btn-success">去结算</a>`。
*   `[✅]` **JavaScript (页面内):**
    *   数量输入框 `onchange` 或 +/- 按钮点击时，通过 AJAX (推荐) 或表单提交到 `CartServlet?action=update` 更新数量。
    *   成功更新后，JS 更新页面上的小计和总计。
    *   (如果不用AJAX，每次数量更改都刷新页面，体验稍差但符合期末项目要求)。
*   `[✅]` **测试:** 添加商品，查看购物车，修改数量，删除商品，清空购物车，空购物车提示，金额计算。

---

## 6. 支付模块 (Payment Module - Simulated)

#### 6.1 控制器层 (CheckoutServlet.java - `com.yycy.controller`)

*   `[✅]` 创建 `CheckoutServlet.java`。
*   `[✅]` 注入 `ICartService cartService = new CartServiceImpl();`, `IUserService userService = new UserServiceImpl();`, `IAddressService addressService = new AddressServiceImpl();`, `IProductService productService = new ProductServiceImpl();`
*   `[✅]` **`doGet` (展示结算页面):**
    *   `HttpSession session = request.getSession();`
    *   `User loggedInUser = (User) session.getAttribute("loggedInUser");`
    *   获取用户默认地址: `Address address = addressService.getAddressByUserId(loggedInUser.getId());`
    *   `request.setAttribute("userAddress", address);`
    *   **判断进入路径:**
        *   **从商品详情页"立即购买":**
            *   获取 `productId`, `quantity` 参数。
            *   `Product product = productService.getProductById(productId);`
            *   创建临时的 `CartItem` 或类似结构，计算总金额。
            *   `request.setAttribute("checkoutItems", Collections.singletonList(new CartItem(product, quantity)));`
            *   `request.setAttribute("checkoutTotal", product.getPrice().multiply(new BigDecimal(quantity)));`
            *   `session.setAttribute("isBuyNow", true);` // 标记为立即购买
            *   `session.setAttribute("buyNowItem", new CartItem(product, quantity));` // 存储立即购买的商品信息
        *   **从购物车"去结算":**
            *   `Cart cart = cartService.getCart(session);`
            *   `request.setAttribute("checkoutItems", new ArrayList<>(cart.getItems().values()));`
            *   `request.setAttribute("checkoutTotal", cart.getTotalPrice());`
            *   `session.removeAttribute("isBuyNow");`
            *   `session.removeAttribute("buyNowItem");`
    *   转发到 `/WEB-INF/jsp/checkout.jsp`。
*   `[✅]` **`doPost` (处理"确认支付", 对应URL `/submitOrder` 或 `CheckoutServlet` 带 `action=submit`):**
    *   `HttpSession session = request.getSession();`
    *   **模拟支付成功。**
    *   如果不是"立即购买" (`session.getAttribute("isBuyNow") == null` 或 `false`):
        *   `cartService.clearCart(session);` // 清空购物车
    *   `session.removeAttribute("isBuyNow");`
    *   `session.removeAttribute("buyNowItem");`
    *   重定向到 `/paymentSuccess` (或 `CheckoutServlet?action=success`)。
*   `[✅]` `web.xml`: 注册 `CheckoutServlet` 到 `/checkout` 和 `/submitOrder` (或用action区分)。

#### 6.2 视图层 (WEB-INF/jsp/checkout.jsp & WEB-INF/jsp/paymentSuccess.jsp)

*   `[✅]` **WEB-INF/jsp/checkout.jsp:**
    *   `checkout.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
    *   **用户信息区域:**
        *   显示用户昵称: `${sessionScope.loggedInUser.nickname}`。
        *   显示收货地址: `${userAddress.province} ${userAddress.city} ...` (如果 `userAddress` 存在)。
        *   显示手机号: `${sessionScope.loggedInUser.phone}`。
        *   (若信息缺失则留空或不显示)。
    *   **商品列表区域 (类似购物车，但数量不可调):**
        *   JSTL `c:forEach var="item" items="${checkoutItems}"`: 图片, 名称, 单价, 购买数量。
    *   **总金额区域:** `<h3>订单总额: ¥${checkoutTotal}</h3>`。
    *   **"确认支付"按钮:** `<form method="post" action="${pageContext.request.contextPath}/submitOrder"><button type="submit" class="btn btn-success">确认支付</button></form>`。
*   `[✅]` **WEB-INF/jsp/paymentSuccess.jsp:**
    *   `paymentSuccess.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
    *   显示"支付成功！"提示。
    *   "返回首页"链接: `<a href="${pageContext.request.contextPath}/home" class="btn btn-primary">返回首页</a>`。
*   `[✅]` `web.xml`: (可选) 创建一个简单的 `PaymentSuccessServlet` 映射到 `/paymentSuccess`，仅用于转发到 `paymentSuccess.jsp`，或者 `CheckoutServlet` 处理 `action=success`。
*   `[✅]` **测试:** 从购物车结算，从商品详情立即购买，支付成功后购物车是否清空，页面跳转。

---

## 7. 个人中心模块 (Personal Center Module)

#### 7.1 DAO 层实现 (UserDaoImpl, AddressDaoImpl - 部分已完成)

*   `[✅]` **UserDaoImpl - `update(User user)`:** 实现完整的用户信息更新 (昵称, 电话, 邮箱, 性别)。
*   `[✅]` **UserDaoImpl - `updatePassword(int userId, String newPassword)`:** (新方法) SQL `UPDATE users SET password = ? WHERE id = ?`。
*   `[✅]` **UserDaoImpl - `updateAvatar(int userId, String avatarPath)`:** (新方法) SQL `UPDATE users SET avatar_path = ? WHERE id = ?`。
*   `[✅]` **AddressDaoImpl - `findByUserId(int userId)`:** 实现。
*   `[✅]` **AddressDaoImpl - `save(Address address)`:** 实现。
*   `[✅]` **AddressDaoImpl - `update(Address address)`:** 实现。

#### 7.2 Service 层实现 (UserServiceImpl, AddressServiceImpl - 部分已完成)

*   `[✅]` **UserServiceImpl - `updateUserProfile(User user)`:** 调用 `userDao.update(user)`。
*   `[✅]` **UserServiceImpl - `changePassword(int userId, String oldPassword, String newPassword)`:**
    *   `User user = userDao.findById(userId);`
    *   校验 `oldPassword` 是否与 `user.getPassword()` 匹配。
    *   校验 `newPassword` 长度 (>=6)。
    *   成功则调用 `userDao.updatePassword(userId, newPassword)`。
*   `[✅]` **UserServiceImpl - `updateUserAvatar(int userId, String avatarPath)`:** 调用 `userDao.updateAvatar(userId, avatarPath)`。
*   `[✅]` **AddressServiceImpl - `getAddressByUserId(int userId)`:** 调用 `addressDao.findByUserId(userId)`。
*   `[✅]` **AddressServiceImpl - `saveOrUpdateAddress(Address address)`:**
    *   `existingAddress = addressDao.findByUserId(address.getUserId());`
    *   如果 `existingAddress == null`, 调用 `addressDao.save(address)`。
    *   否则, 设置 `address.setId(existingAddress.getId())` 并调用 `addressDao.update(address)`。

#### 7.3 控制器层 (ProfileServlet.java - `com.yycy.controller`)

*   `[✅]` 创建 `ProfileServlet.java`。
*   `[✅]` 注入 `IUserService`, `IAddressService`。
*   `[✅]` **`doGet` (展示个人中心主页或特定表单):**
    *   `HttpSession session = request.getSession(); User loggedInUser = (User) session.getAttribute("loggedInUser");`
    *   获取 `action` 参数 (例如 `editProfile`, `changePass`, `manageAddress`, `uploadAvatarForm`)。
    *   默认或 `action="view"`:
        *   `User userDetails = userService.getUserById(loggedInUser.getId());`
        *   `Address address = addressService.getAddressByUserId(loggedInUser.getId());`
        *   `request.setAttribute("userDetails", userDetails);`
        *   `request.setAttribute("userAddress", address);`
        *   转发到 `/WEB-INF/jsp/profile.jsp`。
    *   (根据 action 预加载不同表单所需数据，或由 profile.jsp 通过 JSTL c:if 控制显示哪个表单)。
*   `[✅]` **`doPost` (处理表单提交):**
    *   获取 `action` 参数。
    *   **`action="updateInfo"`:**
        *   获取表单参数 (昵称, 电话, 邮箱, 性别)。
        *   创建 `User` 对象，设置从 session 获取的 `id` 和 `username`，以及表单数据。
        *   调用 `userService.updateUserProfile(userToUpdate)`。
        *   更新 Session 中的 `loggedInUser` 对象的部分信息 (如昵称)。
        *   设置成功/失败消息，重定向回 `/profile`。
    *   **`action="changePassword"`:**
        *   获取旧密码, 新密码, 确认新密码。
        *   校验 (新密码长度, 两次新密码一致)。
        *   调用 `userService.changePassword(loggedInUser.getId(), oldPassword, newPassword)`。
        *   设置成功/失败消息，重定向回 `/profile`。
    *   **`action="uploadAvatar"`:** (使用 Apache Commons FileUpload)
        *   检查是否为 `multipart/form-data`。
        *   解析请求，获取上传的文件项。
        *   保存文件到服务器指定路径 (例如 `images/tou_xiang/user_<id>_<timestamp>.jpg`)。 👤 (路径规划和权限可能需人工)
        *   获取文件相对路径 `avatarPath`。
        *   调用 `userService.updateUserAvatar(loggedInUser.getId(), avatarPath)`。
        *   更新 Session 中 `loggedInUser.setAvatarPath(avatarPath)`。
        *   设置成功/失败消息，重定向回 `/profile`。
        *   **`action="saveAddress"`:**
        *   获取地址表单参数 (省, 市, 区, 详细地址)。
        *   创建 `Address` 对象，设置 `userId`。
        *   调用 `addressService.saveOrUpdateAddress(address)`。
        *   设置成功/失败消息，重定向回 `/profile`。
*   `[✅]` `web.xml`: 注册 `ProfileServlet` 到 `/profile`。

#### 7.4 视图层 (WEB-INF/jsp/profile.jsp)

*   `[✅]` `profile.jsp` 将包含完整的 HTML 结构 (head, body, navigation, content, footer)。
*   `[✅]` **整体布局 (Bootstrap Grid: 左侧导航菜单，右侧内容区):**
    *   **左侧菜单 (Bootstrap Navs or List group):**
        *   链接到: 个人信息 (`/profile?tab=info`), 修改密码 (`/profile?tab=password`), 上传头像 (`/profile?tab=avatar`), 管理地址 (`/profile?tab=address`)。
    *   **右侧内容区 (根据 `param.tab` 或 Servlet 设置的属性动态显示):**
        *   **个人信息表单 (`c:if test="${param.tab == 'info' or empty param.tab}"`):**
            *   用户名 (只读): `${userDetails.username}`。
            *   输入框: 昵称, 电话, 邮箱。
            *   单选按钮: 性别 (男, 女, 保密)。
            *   "保存修改"按钮 (提交到 `ProfileServlet?action=updateInfo`)。
        *   **修改密码表单 (`c:if test="${param.tab == 'password'}"`):**
            *   输入框: 旧密码, 新密码, 确认新密码。
            *   "确认修改"按钮 (提交到 `ProfileServlet?action=changePassword`)。
        *   **上传头像表单 (`c:if test="${param.tab == 'avatar'}"`):**
            *   当前头像预览: `<img src="${pageContext.request.contextPath}/${userDetails.avatarPath != null ? userDetails.avatarPath : 'images/tou_xiang/default.jpg'}" ...>`。
            *   `<form method="post" action="${pageContext.request.contextPath}/profile?action=uploadAvatar" enctype="multipart/form-data">`
                *   `<input type="file" name="avatarFile">`
                *   "保存头像"按钮。
            *   JS 图片本地预览 (可选)。
        *   **管理地址表单 (`c:if test="${param.tab == 'address'}"`):**
            *   输入框: 省, 市, 区, 详细地址 (值来自 `${userAddress}` 如果存在)。
            *   "保存地址"按钮 (提交到 `ProfileServlet?action=saveAddress`)。
    *   显示操作结果消息 (`${requestScope.successMessage}`, `${requestScope.errorMessage}`).
*   `[✅]` **测试:** 各个表单的展示、提交、数据更新、Session 更新、头像上传和显示、地址保存和显示。

---

## 8. 整体测试与部署准备

*   `[✅]` **功能测试:**
    *   `[✅]` 逐一测试 RFC 中定义的所有功能点和用户流程。
    *   `[✅]` 测试边界条件和异常输入 (例如，搜索特殊字符，提交空表单等)。
*   `[✅]` **用户体验测试:**
    *   `[✅]` 检查导航是否清晰，提示信息是否明确。
    *   `[✅]` 检查页面加载速度 (对于期末项目，主要是避免明显卡顿)。
*   `[✅]` **兼容性测试 (基本):**
    *   `[✅]` 在主流浏览器 (Chrome, Firefox, Edge) 上测试基本功能和显示。 👤
*   `[✅]` **数据一致性检查:**
    *   `[✅]` 验证数据库中的数据与前端显示是否一致 (例如，个人信息修改后)。
*   `[✅]` **安全性检查 (初级):**
    *   `[✅]` 确认 `AuthenticationFilter` 按预期工作。
    *   `[✅]` 检查是否有明显的XSS或SQL注入风险 (对于期末项目，主要依赖于正确使用 `PreparedStatement` 和 JSTL 的自动转义)。
    *   `[✅]` **[回顾]** 密码明文存储是已知风险点，符合RFC要求。
*   `[✅]` **代码审查与清理:**
    *   `[✅]` 移除不必要的代码和注释。
    *   `[✅]` 格式化代码。
    *   `[✅]` 检查是否有未处理的异常或资源泄漏 (数据库连接等)。
*   `[✅]` **编写/更新 `README.md`:** 👤
    *   `[✅]` 项目简介。
    *   `[✅]` 技术栈。
    *   `[✅]` 如何配置和运行项目 (数据库设置，Tomcat部署)。
    *   `[✅]` (可选) 已实现功能列表。
*   `[✅]` **最终打包与部署:** 👤
    *   `[✅]` 使用 Maven (`mvn clean package`) 生成 WAR 文件。
    *   `[✅]` 将 WAR 文件部署到目标 Tomcat 服务器。
    *   `[✅]` 进行最终的线上测试 (如果在远程服务器部署)。

---

**注意事项:**
*   本 IMP 是一个详细的指导，但在实际开发中可能会根据遇到的具体问题进行调整。
*   建议频繁进行小步提交到 Git 版本控制系统。
*   对于复杂的 Servlet 逻辑或 JSP 动态内容，可以先实现核心功能，再逐步完善细节和用户体验。
*   **[技术考量]** 贯穿整个开发过程，对于可优化点（如 AJAX 交互、更安全的配置、连接池等）有所意识，但以完成 RFC 要求为首要目标。