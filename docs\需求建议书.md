# 需求建议书 (RFC) - JavaWeb 期末电商平台项目

## 0. 开发环境

tomcat 9 + java EE 8

## 1. 引言 (Introduction)

本文档旨在清晰、完整地定义一个基于 JSPServlet + Bootstrap + JSTL + MySQL8 技术栈的电商平台期末作业项目。该项目旨在实现一个功能简洁但完整的在线购物体验，包含商品展示、搜索、购物车、模拟支付、用户注册登录以及个人中心等核心模块。本文档将作为项目后续设计与开发的指导依据。

**项目目标:**

- 实现一个满足课程要求的、功能完整的电商平台。
- 在功能完整的前提下，力求实现方式的简洁性。
- 为用户提供基本的商品浏览、搜索、购买及账户管理功能。
- 巩固和运用本学期所学的 JavaWeb 相关技术。

## 2. 模块化需求 (Modular Requirements)

### 2.1 首页模块 (Homepage Module)

**目标:** 为用户提供一个直观的商品浏览入口、快速搜索商品以及将商品快速加入购物车的途径。

**核心功能:**

1.  **商品信息展示:**

    - **展示内容 (每个商品卡片，从上到下):**
      - 商品主图片
      - 商品名称
      - 商品价格
      - “加入购物车”按钮
    - **布局:**
      - 商品以卡片式布局进行展示。
      - 项目总共约有 12-16 个商品，每行展示 4 个商品。
    - **排序:**
      - 商品默认按照其在数据库中的顺序进行展示。
    - **分页:**
      - 不提供分页功能。
    - **商品分类:**
      - 不提供商品分类筛选功能。

2.  **商品名称模糊搜索:**

    - **搜索框位置:**
      - 位于页面顶部居中位置。
    - **触发方式:**
      - 用户输入搜索关键词后，点击“搜索”按钮执行搜索。
    - **结果展示:**
      - 搜索结果在当前首页的商品列表区域进行更新并展示。
      - 搜索结果同样采用卡片式布局（一行 4 个，包含主图、名称、价格、“加入购物车”按钮）。
      - 搜索结果有多少就显示多少，不分页（鉴于总商品数量有限，此方式可行）。
    - **无结果提示:**
      - 若无商品匹配搜索关键词，则在商品列表区域显示明确的提示信息（例如：“抱歉，没有找到与‘[关键词]’相关的商品”）。

3.  **首页“加入购物车”按钮交互:**
    - **位置:** 每个商品卡片的价格下方。
    - **数量:** 点击按钮默认将 1 件该商品加入购物车（不提供数量选择器）。
    - **用户反馈:**
      - 成功加入后，显示简短提示信息（例如：“已成功加入购物车”）。
      - 顶部导航栏的购物车图标（如果存在角标）数量 +1。
    - **未登录状态:** 若用户未登录，点击“加入购物车”按钮将直接跳转到登录页面。

**其他页面元素:**

1.  **网站 Logo:**
    - 放置在页面顶部导航栏左侧。
    - 点击 Logo 应能刷新或返回到首页默认状态（即展示所有商品，清除搜索状态）。
2.  **顶部导航栏:**
    - **未登录状态下显示:**
      - “登录”链接 (点击跳转至登录页面)
      - “注册”链接 (点击跳转至注册页面)
      - “购物车”链接 (点击跳转至登录页面，提示用户先登录)
    - **登录状态下显示:**
      - “个人中心”链接 (点击跳转至个人中心页面)
      - “购物车”链接 (点击跳转至购物车页面，并显示角标数量)
      - “退出登录”链接 (点击执行退出登录操作，并通常返回首页)
3.  **轮播图 (Banner):**
    - 位于页面主要内容区域上方（例如，导航栏下方，商品列表上方）。
    - 展示 3 张固定的图片（图片内容可为精选商品或促销信息，无需从数据库读取）。
    - 点击轮播图图片不执行任何跳转操作。

---

### 2.2 商品详情模块 (Product Detail Module)

**目标:** 向用户详细展示特定商品的信息，并提供加入购物车和直接购买的操作。同时提供快速导航到其他商品详情页的途径。

**核心功能:**

1.  **商品信息展示:**

    - **页面主要内容区域 (例如，右侧):**
      - 商品名称
      - 商品价格
      - 商品详细描述 (纯文本，全部展示)
    - **图片展示区域 (例如，左侧):**
      - 一个较大的主图片显示区域。
      - 主图侧边有一列（或一行）较小的缩略图 (展示同一商品的不同角度/细节图片，共约 3-4 张)。
      - 用户点击缩略图时，主图片显示区域会更新为对应的大图。
      - 不提供图片放大镜/悬浮放大效果。

2.  **用户操作区域 (通常在商品信息下方):**

    - **数量选择器:**
      - 提供数量输入框以及“+”和“-”按钮，允许用户调整希望购买的商品数量。
      - 默认数量为 1。
    - **“加入购物车”按钮:**
      - 点击后，将当前详情页的商品（按选定数量）加入购物车。
      - 用户反馈:
        - 显示简短提示信息（例如：“已成功加入购物车”）。
        - 顶部导航栏的购物车图标（如果存在角标）数量更新。
      - 页面行为: 停留在当前商品详情页。
      - 未登录状态: 若用户未登录，点击此按钮应跳转到登录页面。
    - **“立即购买”按钮:**
      - 点击后，将当前详情页的商品（按选定数量）直接引导至模拟支付流程。
      - 跳过购物车步骤。
      - 需要将商品信息（如商品 ID、数量、价格等）传递给支付模块。
      - 未登录状态: 若用户未登录，点击此按钮应跳转到登录页面。

3.  **迷你商品导航列表 (替代传统规格选择区域):**
    - 在商品详情页的某个区域（例如，在商品描述下方或侧边栏），展示一个**所有商品**的迷你列表。
    - 每个列表项展示该商品的主图片和商品名称。
    - 布局类似于首页的商品卡片，但可能更紧凑。
    - 用户点击此列表中的任何一个商品，会跳转到对应商品的详情页。

**页面进入与异常处理:**

1.  **进入方式:** 用户通过点击首页或其他页面的商品链接（通常传递商品 ID）进入商品详情页。
2.  **无效商品 ID:** 如果用户尝试访问一个不存在的商品 ID 的详情页，系统应显示一个明确的“商品未找到”的提示页面。

**其他页面元素:**

1.  **顶部导航栏:**
    - 与首页保持一致，包含 Logo、登录/注册/购物车/个人中心/退出登录等链接。

---

### 2.3 购物车模块 (Shopping Cart Module)

**目标:** 允许用户查看已加入购物车的商品，管理商品数量，并进行结算。

**核心功能:**

1.  **购物车商品展示:**

    - **展示内容 (每个商品项):**
      - 商品主图片
      - 商品名称
      - 商品单价
      - 用户选择的数量 (可编辑)
      - 该商品的小计金额 (单价 \* 数量)
    - **空购物车提示:**
      - 若购物车内无商品，则在页面主要区域显示明确的提示信息（例如：“您的购物车还是空的，快去挑选喜爱的商品吧！”并可能提供一个“去逛逛”的链接返回首页）。
    - **商品排序:**
      - 购物车中的商品按照加入购物车的时间倒序排列（即最新加入的商品显示在最上面）。

2.  **调整商品数量:**

    - **调整方式:**
      - 为每个商品项提供一个数量输入框。
      - 同时在数量输入框两侧提供“+”和“-”按钮。
    - **数量限制:**
      - 商品数量最小值为 1。当数量为 1 时，“-”按钮可能置灰或点击无效。
    - **实时更新:**
      - 当用户调整商品数量后，该商品的小计金额以及整个购物车的合计金额应实时更新并显示在页面上。

3.  **金额计算与展示:**

    - **小计金额:**
      - 指单个商品品类的总金额，计算方式为：`商品单价 * 该商品数量`。
      - 显示在每个商品项中。
    - **合计金额:**
      - 指购物车内所有已选商品的小计金额之和。
      - 显示在购物车商品列表区域的右下方。

4.  **购物车操作:**
    - **删除单个商品:**
      - 在每个商品项旁边提供一个“删除”按钮或图标。
      - 点击后，该商品项从购物车中移除，并实时更新合计金额。
    - **清空购物车:**
      - 提供一个“清空购物车”按钮（例如，在商品列表上方或合计金额区域附近）。
      - 点击后，移除购物车中所有商品，并实时更新合计金额（变为 0），同时显示空购物车提示。
    - **去结算:**
      - 提供一个明确的“去结算”或“去支付”按钮。
      - 该按钮通常位于合计金额旁边（例如，在合计金额的右侧）。
      - 点击后，用户将被引导至支付模块。

**用户状态与访问控制:**

1.  **访问权限:**
    - 购物车页面仅对已登录用户开放。
    - 若未登录用户尝试访问购物车页面（无论是通过导航栏链接还是直接输入 URL），都应被重定向到登录页面。

**页面布局与基本元素:**

1.  **整体布局:**
    - 页面主要区域用于展示购物车中的商品列表。
    - 页面的右下方（或一个固定区域）用于显示合计金额、“清空购物车”（可选位置）和“去结算”按钮。
2.  **顶部导航栏:**
    - 与网站其他页面保持一致，根据用户登录状态显示相应的导航链接（如 Logo、个人中心、退出登录等）。

---

### 2.4 支付模块 (Payment Module)

**目标:** 为用户提供一个模拟的支付流程，完成购买操作。

**核心功能:**

1.  **进入支付模块:**

    - **路径 1 (从购物车):** 用户在购物车页面点击“去结算”按钮后进入。
      - **传递数据:** 购物车中所有待结算的商品列表（包含每个商品的名称、主图片、单价、购买数量）以及订单总金额。
    - **路径 2 (从商品详情页):** 用户在商品详情页点击“立即购买”按钮后进入。
      - **传递数据:** 当前商品的名称、主图片、单价、购买数量以及该笔订单的总金额。
    - **数据处理:** 两种路径传递过来的商品信息和总金额，在支付页面以统一的格式展示。

2.  **支付页面信息展示:**

    - **整体布局参考:** 页面结构可以参考购物车页面的商品列表展示方式。
    - **用户信息区域 (位于商品列表上方):**
      - 展示当前登录用户的**用户昵称** (如果用户已设置，否则可显示用户名或留空)。
      - 展示用户的默认收货地址（如果用户已设置）。
      - 展示用户的手机号码（如果用户已设置）。
      - _注: 如果用户信息（昵称、地址、电话）缺失，则对应区域留空或不显示，不强制用户必须填写才能支付。_
    - **商品列表区域:**
      - 列出所有待支付的商品。
      - 每个商品项展示：商品主图片、商品名称、商品单价、购买数量。
      - _注: 此处的商品数量不可调整。_
    - **总金额区域 (位于商品列表下方):**
      - 清晰展示订单的总支付金额。
    - **模拟支付按钮:**
      - 一个明确的“确认支付”或“模拟支付”按钮，位于总金额区域下方或旁边。

3.  **模拟支付交互流程:**

    - **触发支付:** 用户点击“确认支付”按钮。
    - **支付结果:** 系统不进行实际的支付操作，直接模拟支付成功。
    - **反馈:** 页面跳转到一个新的、独立的“支付成功”确认页面。

4.  **支付成功后的操作:**

    - **购物车处理:** 如果此次支付源自购物车，则支付成功后，购物车中对应的已支付商品将被清空。
    - **订单记录:** 无需在数据库中创建详细的订单记录。
    - **“支付成功”页面:**
      - 显示简洁的“支付成功！”或类似提示信息。
      - 提供一个“确认”或“返回首页”的按钮/链接，点击后**跳转到商店首页**。

5.  **支付失败处理:**
    - 不处理支付失败的逻辑，总是模拟支付成功。

**页面布局与基本元素:**

1.  **整体结构 (从上到下):**
    - 顶部导航栏。
    - 用户信息展示区域。
    - 待支付商品列表区域。
    - 订单总金额。
    - “确认支付”按钮。
2.  **顶部导航栏:**
    - 与网站其他页面保持一致，根据用户登录状态显示相应的导航链接。

---

### 2.5 注册模块 (Registration Module)

**目标:** 允许新用户创建账户。

**核心功能:**

1.  **用户输入信息:**

    - **必填字段:**
      - **用户名:** 用于登录，必须在系统中唯一。无特殊字符或长度限制（由数据库本身限制决定）。
      - **密码:** 用户账户的访问凭证。长度必须大于等于 6 位，不限制字符类型。
      - **确认密码:** 用于验证用户输入的密码是否一致。
    - **可选/后续补充字段:**
      - 邮箱、手机号、用户昵称等信息不在注册时收集，用户可在注册成功后跳转至个人中心进行补充。

2.  **注册流程与校验:**
    - 用户填写完注册表单并点击“注册”按钮。
    - **后端校验:**
      - 检查用户名是否已存在于数据库中。
      - 检查密码长度是否大于等于 6 位。
      - 检查“确认密码”是否与“密码”字段一致。
    - **错误提示:**
      - 如果校验失败（例如，用户名已存在、密码长度不足、两次密码不一致），在对应输入框旁边显示清晰的错误提示信息。
    - **注册成功:**
      - 所有校验通过后，将用户信息（用户名和密码）存入数据库。
      - 向用户显示简短的“注册成功”提示。
      - 页面自动跳转到**个人中心页面**，以便用户补充其他信息。

**页面布局与元素:**

1.  **主要元素:**
    - 页面标题，例如：“用户注册”。
    - 注册表单，包含用户名、密码、确认密码的输入框。
    - “注册”按钮。
    - 一个指向登录页面的链接，例如：“已有账号？去登录”。
2.  **导航栏:**
    - 显示简化的顶部导航栏，例如只包含网站 Logo（点击返回首页）和必要的全局链接（如果适用，但一般注册登录页导航较少）。

---

### 2.6 登录模块 (Login Module)

**目标:** 允许已注册用户访问其账户。

**核心功能:**

1.  **用户输入信息:**

    - **必填字段:**
      - **用户名:** 用户注册时使用的用户名。
      - **密码:** 用户对应的账户密码。

2.  **登录流程与校验:**
    - 用户填写完登录表单并点击“登录”按钮。
    - **后端校验:**
      - 根据用户输入的用户名查询数据库。
      - 如果用户名存在，则校验用户输入的密码与数据库中存储的密码是否匹配。
    - **错误提示:**
      - 如果登录失败（例如，用户名不存在或密码错误），在表单区域（例如，表单顶部或按钮下方）显示统一的错误提示信息，例如：“用户名或密码错误”。
    - **登录成功:**
      - 密码校验通过后，创建用户会话 (Session)。
      - 页面自动跳转到**商店首页**。

**页面布局与元素:**

1.  **主要元素:**
    - 页面标题，例如：“用户登录”。
    - 登录表单，包含用户名和密码的输入框。
    - “登录”按钮。
    - 一个指向注册页面的链接，例如：“没有账号？去注册”。
2.  **导航栏:**
    - 与注册页面类似，显示简化的顶部导航栏。

---

### 2.7 个人中心模块 (Personal Center Module)

**目标:** 允许登录用户管理其账户信息、安全设置和个人资料。

**整体布局:**

- 页面左侧为功能导航菜单（例如：个人信息、修改密码、上传头像、管理地址——可根据实际情况调整菜单项命名和分组）。
- 页面右侧根据左侧菜单的选择，显示对应的功能表单或信息展示区域。
- 页面顶部依然显示标准的网站导航栏。

**核心功能:**

1.  **个人信息 (或可命名为“修改资料”):**

    - **可修改字段:**
      - **用户昵称:** 提供输入框供用户修改或首次设置。
      - **电话号码:** 提供输入框供用户补充或修改。无需严格格式校验。
      - **邮箱地址:** 提供输入框供用户补充或修改。无需严格格式校验。
      - **性别:** 提供单选按钮组，选项为：男、女、保密。
    - **固定展示字段:**
      - **用户名 (账号):** 仅展示，不可修改。
    - **操作:**
      - 提供“保存修改”按钮。
      - 保存成功后，在页面给予明确提示（例如：“资料更新成功！”）。
      - 所有修改信息持久化到数据库。

2.  **修改密码:**

    - **输入字段:**
      - 旧密码
      - 新密码
      - 确认新密码
    - **校验规则:**
      - 新密码长度需大于等于 6 位（与注册时一致）。
      - 确认新密码必须与新密码一致。
      - 旧密码必须与数据库中当前密码匹配。
    - **反馈:**
      - 在对应输入框旁边显示校验结果或错误提示（例如：“旧密码错误”、“两次新密码输入不一致”、“新密码长度不足”）。
      - 成功修改密码后，在页面给予明确提示（例如：“密码修改成功”）。不强制用户重新登录。
    - **操作:**
      - 提供“确认修改”按钮。
      - 新密码持久化到数据库。

3.  **上传头像:**

    - **交互界面:**
      - 一个图片预览区域，用于显示当前头像或待上传的头像。
      - 一个“选择文件”按钮，允许用户从本地选择图片。
      - 选择图片后，预览区域应更新显示用户选择的图片。
    - **图片处理:**
      - 支持常见的图片格式（如 JPG, PNG）。无需严格的大小限制，但后端应能处理合理大小的图片。
    - **操作:**
      - 提供“保存头像”按钮。
      - 上传成功后，头像信息（例如图片路径）持久化到数据库。
      - 页面给予明确提示（例如：“头像上传成功！”）。
      - 页面上其他显示该用户头像的地方（如顶部导航栏）应**立即更新**为新头像。

4.  **管理地址 (单一地址管理):**
    - **目标:** 用户可以管理一个收货地址。
    - **输入字段 (用户自行输入文本):**
      - 省
      - 市
      - 区
      - 详细地址 (例如：街道、门牌号)
    - **展示:**
      - 如果用户已保存地址，则显示当前地址信息。
    - **操作:**
      - 提供“保存地址”按钮。
      - 用户可以新增或修改其唯一的收货地址。
      - 保存成功后，在页面给予明确提示（例如：“地址保存成功！”）。
      - 地址信息持久化到数据库。

**数据持久化:**

- 所有用户在个人中心所做的修改（昵称、电话、邮箱、性别、密码【注：此处密码为明文更新】、头像信息、地址信息）均需更新到数据库中对应的用户记录。

**补充：**  
- 在数据库中的邮箱字段添加唯一约束，用户在个人中心补充或修改邮箱时，需要异步校验数据库邮箱是否唯一，在输入框旁边提醒邮箱可用/重复（参考注册页面）。

## 3. 非功能性需求 (Non-Functional Requirements) - 初步

- **易用性:** 界面应简洁直观，用户操作流程应符合基本使用习惯。
- **可靠性:** 基本功能应能稳定运行，数据操作（如注册、购物车、个人信息修改）应准确无误。
- **技术栈符合性:** 严格按照要求的 JSPServlet + Bootstrap + JSTL + MySQL8 实现。

## 4. 范围边界 / 非目标 (Scope Boundaries / Non-Goals)

- 不包含复杂的订单管理系统（如订单状态跟踪、退款退货等）。
- 不包含在线客服功能。
- 不包含商品评价系统。
- 不包含优惠券、促销活动等复杂营销功能。
- 不包含管理员后台管理功能（如商品上下架、用户管理等）。
- 不追求极致的性能优化和高并发处理能力。
- 不涉及真实的支付接口对接。
